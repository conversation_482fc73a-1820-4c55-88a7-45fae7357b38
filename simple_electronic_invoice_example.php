<?php
/**
 * 微信电子发票接口简化使用示例
 * 
 * 此示例展示了如何使用简化版接口，只需要传入必填参数
 */

require_once 'app/common/lib/wechat/ElectronicInvoiceClient.php';

use app\common\lib\wechat\ElectronicInvoiceClient;

// 创建电子发票客户端实例
$client = new ElectronicInvoiceClient();

try {
    // ========== 步骤1：上传电子发票文件 ==========
    
    $invoiceFilePath = '/path/to/your/invoice.pdf';
    $uploadResponse = $client->uploadFapiaoFile($invoiceFilePath);
    
    if ($uploadResponse->httpCode !== 200) {
        throw new Exception('上传失败: ' . $uploadResponse->body);
    }
    
    $uploadResult = json_decode($uploadResponse->body, true);
    $fapiaoMediaId = $uploadResult['fapiao_media_id'];
    echo "文件上传成功，文件ID: {$fapiaoMediaId}\n";
    
    // ========== 步骤2：插入电子发票到用户卡包（简化版） ==========
    
    $insertResponse = $client->insertFapiaoCardsSimple(
        fapiaoApplyId: 'ORDER_20240101_001',           // 发票申请单号（订单号）
        scene: ElectronicInvoiceClient::SceneWithWechatPay, // 微信支付场景
        buyerType: 'INDIVIDUAL',                       // 个人
        buyerName: '张三',                             // 购买方名称
        fapiaoMediaId: $fapiaoMediaId,                 // 上传返回的文件ID
        fapiaoNumber: '12345678',                      // 发票号码
        fapiaoCode: '123456789012',                    // 发票代码
        fapiaoTime: '2024-01-01T10:00:00+08:00',      // 开票时间
        checkCode: '12345',                            // 校验码
        password: 'invoice_password_here',             // 发票密码
        totalAmount: 10000,                            // 总价税合计（分）
        taxAmount: 1000,                               // 总税额（分）
        amount: 9000,                                  // 总金额（分）
        sellerName: '深圳新能智慧充电科技有限公司',      // 销售方名称
        sellerTaxpayerId: '91440300123456789X',        // 销售方纳税人识别号
        drawer: '开票员',                              // 开票人
        phone: '13800138000',                          // 手机号（可选）
        email: '<EMAIL>'                  // 邮箱（可选）
    );
    
    if ($insertResponse->httpCode === 200) {
        echo "电子发票插入用户卡包成功\n";
    } else {
        echo "插入失败: " . $insertResponse->body . "\n";
    }
    
} catch (Exception $e) {
    echo "操作失败: " . $e->getMessage() . "\n";
}

// ========== 企业用户示例 ==========
/*
// 如果是企业用户，需要提供纳税人识别号
$insertResponse = $client->insertFapiaoCardsSimple(
    fapiaoApplyId: 'ORDER_20240101_002',
    scene: ElectronicInvoiceClient::SceneWithWechatPay,
    buyerType: 'ORGANIZATION',                         // 企业
    buyerName: '深圳某某科技有限公司',
    fapiaoMediaId: $fapiaoMediaId,
    fapiaoNumber: '87654321',
    fapiaoCode: '210987654321',
    fapiaoTime: '2024-01-01T14:00:00+08:00',
    checkCode: '54321',
    password: 'enterprise_invoice_password',
    totalAmount: 50000,
    taxAmount: 5000,
    amount: 45000,
    sellerName: '深圳新能智慧充电科技有限公司',
    sellerTaxpayerId: '91440300123456789X',
    drawer: '开票员',
    buyerTaxpayerId: '91440300987654321A',              // 企业必填
    phone: '13900139000',
    email: '<EMAIL>'
);
*/
