# 微信电子发票接口实现说明

## 概述

本实现为微信支付电子发票相关接口提供了完整的PHP客户端，包括：

1. **上传电子发票文件接口** (`uploadFapiaoFile`)
2. **将电子发票插入微信用户卡包接口** (`insertFapiaoCards`)

## 功能特性

### ✅ 已实现功能

- [x] 电子发票文件上传（支持PDF格式）
- [x] 文件摘要计算（SM3算法，降级支持SHA256）
- [x] multipart/form-data 表单构建
- [x] 敏感字段自动加密（手机号、邮箱）
- [x] 微信支付公钥加密
- [x] 完整的错误处理
- [x] 简化版接口（只需必填参数）

### 🔧 技术实现

- **签名算法**: WECHATPAY2-SHA256-RSA2048
- **加密算法**: RSA-OAEP（敏感字段）
- **摘要算法**: SM3（降级SHA256）
- **数据格式**: JSON + multipart/form-data

## 使用方法

### 基础用法

```php
use app\common\lib\wechat\ElectronicInvoiceClient;

$client = new ElectronicInvoiceClient();

// 1. 上传电子发票文件
$uploadResponse = $client->uploadFapiaoFile('/path/to/invoice.pdf');
$uploadResult = json_decode($uploadResponse->body, true);
$fapiaoMediaId = $uploadResult['fapiao_media_id'];

// 2. 插入电子发票到用户卡包
$insertResponse = $client->insertFapiaoCards(
    $fapiaoApplyId,
    $scene,
    $buyerInformation,
    $fapiaoCardInformation
);
```

### 简化用法（推荐）

```php
// 使用简化接口，只需传入必填参数
$response = $client->insertFapiaoCardsSimple(
    fapiaoApplyId: 'ORDER_20240101_001',
    scene: ElectronicInvoiceClient::SceneWithWechatPay,
    buyerType: 'INDIVIDUAL',
    buyerName: '张三',
    fapiaoMediaId: $fapiaoMediaId,
    fapiaoNumber: '12345678',
    fapiaoCode: '123456789012',
    fapiaoTime: '2024-01-01T10:00:00+08:00',
    checkCode: '12345',
    password: 'invoice_password',
    totalAmount: 10000,
    taxAmount: 1000,
    amount: 9000,
    sellerName: '深圳新能智慧充电科技有限公司',
    sellerTaxpayerId: '91440300123456789X',
    drawer: '开票员',
    phone: '13800138000',  // 可选，自动加密
    email: '<EMAIL>'  // 可选，自动加密
);
```

## 配置要求

### 必需配置项

确保 `config/wechat.php` 中包含以下配置：

```php
return [
    'appid' => env('WECHAT.APPID'),
    'mch_id' => env('WECHAT.MCH_ID'),
    'api_key_v3' => env('WECHAT.API_KEY_V3'),
    'weixin_pingtai_sn' => env('WECHAT.WEIXIN_PINGTAI_SN'),  // 微信支付平台证书序列号
    'apiclient_key_sn' => env('WECHAT.API_CLIENT_KEY_SN'),
    'apiclient_key' => implode("\n", env('WECHAT.API_CLIENT_KEY', [])),
];
```

### 环境变量配置

在 `.env` 文件中设置：

```ini
[WECHAT]
APPID=your_wechat_appid
MCH_ID=your_merchant_id
API_KEY_V3=your_api_v3_key
WEIXIN_PINGTAI_SN=your_platform_certificate_serial_number
API_CLIENT_KEY_SN=your_client_certificate_serial_number
```

## 重要说明

### 1. SM3算法支持

当前实现会按以下优先级查找SM3算法：
1. `sm3()` 函数（如果有专门的SM3扩展）
2. `hash('sm3', $data)` （如果OpenSSL支持SM3）
3. `hash('sha256', $data)` （降级方案，仅用于开发测试）

**生产环境建议**：
- 安装支持SM3的PHP扩展
- 或使用专门的SM3算法库

### 2. 文件上传限制

- 支持格式：PDF、OFD
- 文件大小：不超过2MB
- 文件ID有效期：3天

### 3. 敏感字段加密

以下字段会自动使用微信支付公钥加密：
- `phone`（手机号）
- `email`（邮箱地址）

### 4. 错误处理

```php
try {
    $response = $client->uploadFapiaoFile($filePath);
    if ($response->httpCode !== 200) {
        $error = json_decode($response->body, true);
        echo "错误: " . $error['message'];
    }
} catch (Exception $e) {
    echo "异常: " . $e->getMessage();
}
```

## 常见问题

### Q: 上传文件时提示签名错误？
A: 检查multipart表单的构建是否正确，确保签名时使用的是meta的JSON字符串。

### Q: 敏感字段加密失败？
A: 确保微信支付平台证书序列号配置正确，且能正常获取平台证书。

### Q: SM3摘要计算不正确？
A: 安装支持SM3的PHP扩展，或在生产环境中使用专门的SM3库。

### Q: 插卡时提示参数错误？
A: 检查必填字段是否完整，特别是企业类型用户的纳税人识别号。

## 参考文档

- [微信支付电子发票开发文档](https://pay.weixin.qq.com/docs/merchant/apis/fapiao/fapiao-card/upload-fapiao-file.html)
- [微信支付签名算法](https://pay.weixin.qq.com/docs/merchant/development/signature.html)
- [微信支付敏感信息加密](https://pay.weixin.qq.com/docs/merchant/development/encrypt.html)
