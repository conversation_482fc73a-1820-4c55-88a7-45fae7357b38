<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 销售方信息实体类
 */
class SellerInformation
{
    /**
     * @var string 销售方名称
     */
    public string $name;

    /**
     * @var string 纳税人识别号
     */
    public string $taxpayerId;

    /**
     * @var string|null 地址
     */
    public ?string $address = null;

    /**
     * @var string|null 电话
     */
    public ?string $telephone = null;

    /**
     * @var string|null 开户银行
     */
    public ?string $bankName = null;

    /**
     * @var string|null 银行账号
     */
    public ?string $bankAccount = null;

    /**
     * 构造函数
     *
     * @param string $name 销售方名称
     * @param string $taxpayerId 纳税人识别号
     */
    public function __construct(string $name, string $taxpayerId)
    {
        $this->name = $name;
        $this->taxpayerId = $taxpayerId;
    }

    /**
     * 设置地址
     *
     * @param string $address 地址
     * @return $this
     */
    public function setAddress(string $address): self
    {
        $this->address = $address;
        return $this;
    }

    /**
     * 设置电话
     *
     * @param string $telephone 电话
     * @return $this
     */
    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;
        return $this;
    }

    /**
     * 设置开户银行
     *
     * @param string $bankName 开户银行
     * @return $this
     */
    public function setBankName(string $bankName): self
    {
        $this->bankName = $bankName;
        return $this;
    }

    /**
     * 设置银行账号
     *
     * @param string $bankAccount 银行账号
     * @return $this
     */
    public function setBankAccount(string $bankAccount): self
    {
        $this->bankAccount = $bankAccount;
        return $this;
    }

    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'name' => $this->name,
            'taxpayer_id' => $this->taxpayerId
        ];

        if (!is_null($this->address)) {
            $data['address'] = $this->address;
        }

        if (!is_null($this->telephone)) {
            $data['telephone'] = $this->telephone;
        }

        if (!is_null($this->bankName)) {
            $data['bank_name'] = $this->bankName;
        }

        if (!is_null($this->bankAccount)) {
            $data['bank_account'] = $this->bankAccount;
        }

        return $data;
    }
}
