<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 电子发票卡券信息实体类
 */
class FapiaoCardInfo
{
    /**
     * @var string 电子发票文件ID
     */
    public string $fapiaoMediaId;

    /**
     * @var string 发票号码
     */
    public string $fapiaoNumber;

    /**
     * @var string 发票代码
     */
    public string $fapiaoCode;

    /**
     * @var string 开票时间 RFC3339格式
     */
    public string $fapiaoTime;

    /**
     * @var string 校验码
     */
    public string $checkCode;

    /**
     * @var string|null 密码区
     */
    public ?string $password = null;

    /**
     * @var int 价税合计金额（分）
     */
    public int $totalAmount;

    /**
     * @var int 税额（分）
     */
    public int $taxAmount;

    /**
     * @var int 不含税金额（分）
     */
    public int $amount;

    /**
     * @var SellerInformation 销售方信息
     */
    public SellerInformation $sellerInformation;

    /**
     * @var ExtraInformation|null 额外信息
     */
    public ?ExtraInformation $extraInformation = null;

    /**
     * @var FapiaoItem[] 发票明细
     */
    public array $items = [];

    /**
     * @var string|null 备注
     */
    public ?string $remark = null;

    /**
     * 构造函数
     *
     * @param string $fapiaoMediaId 电子发票文件ID
     * @param string $fapiaoNumber 发票号码
     * @param string $fapiaoCode 发票代码
     * @param string $fapiaoTime 开票时间
     * @param string $checkCode 校验码
     * @param int $totalAmount 价税合计金额（分）
     * @param int $taxAmount 税额（分）
     * @param int $amount 不含税金额（分）
     * @param SellerInformation $sellerInformation 销售方信息
     */
    public function __construct(
        string $fapiaoMediaId,
        string $fapiaoNumber,
        string $fapiaoCode,
        string $fapiaoTime,
        string $checkCode,
        int $totalAmount,
        int $taxAmount,
        int $amount,
        SellerInformation $sellerInformation
    ) {
        $this->fapiaoMediaId = $fapiaoMediaId;
        $this->fapiaoNumber = $fapiaoNumber;
        $this->fapiaoCode = $fapiaoCode;
        $this->fapiaoTime = $fapiaoTime;
        $this->checkCode = $checkCode;
        $this->totalAmount = $totalAmount;
        $this->taxAmount = $taxAmount;
        $this->amount = $amount;
        $this->sellerInformation = $sellerInformation;
    }

    /**
     * 设置密码区
     *
     * @param string $password 密码区
     * @return $this
     */
    public function setPassword(string $password): self
    {
        $this->password = $password;
        return $this;
    }

    /**
     * 设置额外信息
     *
     * @param ExtraInformation $extraInformation 额外信息
     * @return $this
     */
    public function setExtraInformation(ExtraInformation $extraInformation): self
    {
        $this->extraInformation = $extraInformation;
        return $this;
    }

    /**
     * 添加发票明细
     *
     * @param FapiaoItem $item 发票明细
     * @return $this
     */
    public function addItem(FapiaoItem $item): self
    {
        $this->items[] = $item;
        return $this;
    }

    /**
     * 设置备注
     *
     * @param string $remark 备注
     * @return $this
     */
    public function setRemark(string $remark): self
    {
        $this->remark = $remark;
        return $this;
    }

    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'fapiao_media_id' => $this->fapiaoMediaId,
            'fapiao_number' => $this->fapiaoNumber,
            'fapiao_code' => $this->fapiaoCode,
            'fapiao_time' => $this->fapiaoTime,
            'check_code' => $this->checkCode,
            'total_amount' => $this->totalAmount,
            'tax_amount' => $this->taxAmount,
            'amount' => $this->amount,
            'seller_information' => $this->sellerInformation->toArray()
        ];

        if (!is_null($this->password)) {
            $data['password'] = $this->password;
        }

        if (!is_null($this->extraInformation)) {
            $data['extra_information'] = $this->extraInformation->toArray();
        }

        if (!empty($this->items)) {
            $data['items'] = array_map(function (FapiaoItem $item) {
                return $item->toArray();
            }, $this->items);
        }

        if (!is_null($this->remark)) {
            $data['remark'] = $this->remark;
        }

        return $data;
    }
}
