<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 购买方信息实体类
 */
class BuyerInformation
{
    /**
     * @var string 抬头类型 INDIVIDUAL:个人 ORGANIZATION:企业
     */
    public string $type;

    /**
     * @var string 抬头名称
     */
    public string $name;

    /**
     * @var string|null 纳税人识别号
     */
    public ?string $taxpayerId = null;

    /**
     * @var string|null 地址
     */
    public ?string $address = null;

    /**
     * @var string|null 电话
     */
    public ?string $telephone = null;

    /**
     * @var string|null 开户银行
     */
    public ?string $bankName = null;

    /**
     * @var string|null 银行账号
     */
    public ?string $bankAccount = null;

    /**
     * @var string|null 手机号
     */
    public ?string $phone = null;

    /**
     * @var string|null 邮箱
     */
    public ?string $email = null;

    /**
     * 构造函数
     *
     * @param string $type 抬头类型
     * @param string $name 抬头名称
     */
    public function __construct(string $type, string $name)
    {
        $this->type = $type;
        $this->name = $name;
    }

    /**
     * 设置纳税人识别号
     *
     * @param string $taxpayerId 纳税人识别号
     * @return $this
     */
    public function setTaxpayerId(string $taxpayerId): self
    {
        $this->taxpayerId = $taxpayerId;
        return $this;
    }

    /**
     * 设置地址
     *
     * @param string $address 地址
     * @return $this
     */
    public function setAddress(string $address): self
    {
        $this->address = $address;
        return $this;
    }

    /**
     * 设置电话
     *
     * @param string $telephone 电话
     * @return $this
     */
    public function setTelephone(string $telephone): self
    {
        $this->telephone = $telephone;
        return $this;
    }

    /**
     * 设置开户银行
     *
     * @param string $bankName 开户银行
     * @return $this
     */
    public function setBankName(string $bankName): self
    {
        $this->bankName = $bankName;
        return $this;
    }

    /**
     * 设置银行账号
     *
     * @param string $bankAccount 银行账号
     * @return $this
     */
    public function setBankAccount(string $bankAccount): self
    {
        $this->bankAccount = $bankAccount;
        return $this;
    }

    /**
     * 设置手机号
     *
     * @param string $phone 手机号
     * @return $this
     */
    public function setPhone(string $phone): self
    {
        $this->phone = $phone;
        return $this;
    }

    /**
     * 设置邮箱
     *
     * @param string $email 邮箱
     * @return $this
     */
    public function setEmail(string $email): self
    {
        $this->email = $email;
        return $this;
    }

    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'type' => $this->type,
            'name' => $this->name
        ];

        if (!is_null($this->taxpayerId)) {
            $data['taxpayer_id'] = $this->taxpayerId;
        }

        if (!is_null($this->address)) {
            $data['address'] = $this->address;
        }

        if (!is_null($this->telephone)) {
            $data['telephone'] = $this->telephone;
        }

        if (!is_null($this->bankName)) {
            $data['bank_name'] = $this->bankName;
        }

        if (!is_null($this->bankAccount)) {
            $data['bank_account'] = $this->bankAccount;
        }

        if (!is_null($this->phone)) {
            $data['phone'] = $this->phone;
        }

        if (!is_null($this->email)) {
            $data['email'] = $this->email;
        }

        return $data;
    }
}
