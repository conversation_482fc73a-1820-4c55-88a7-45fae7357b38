<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat\entity\request;

/**
 * 额外信息实体类
 */
class ExtraInformation
{
    /**
     * @var string|null 收款人
     */
    public ?string $payee = null;

    /**
     * @var string|null 复核人
     */
    public ?string $reviewer = null;

    /**
     * @var string|null 开票人
     */
    public ?string $drawer = null;

    /**
     * 设置收款人
     *
     * @param string $payee 收款人
     * @return $this
     */
    public function setPayee(string $payee): self
    {
        $this->payee = $payee;
        return $this;
    }

    /**
     * 设置复核人
     *
     * @param string $reviewer 复核人
     * @return $this
     */
    public function setReviewer(string $reviewer): self
    {
        $this->reviewer = $reviewer;
        return $this;
    }

    /**
     * 设置开票人
     *
     * @param string $drawer 开票人
     * @return $this
     */
    public function setDrawer(string $drawer): self
    {
        $this->drawer = $drawer;
        return $this;
    }

    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [];

        if (!is_null($this->payee)) {
            $data['payee'] = $this->payee;
        }

        if (!is_null($this->reviewer)) {
            $data['reviewer'] = $this->reviewer;
        }

        if (!is_null($this->drawer)) {
            $data['drawer'] = $this->drawer;
        }

        return $data;
    }
}
