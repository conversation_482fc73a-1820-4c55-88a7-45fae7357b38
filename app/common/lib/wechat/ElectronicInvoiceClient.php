<?php
/** @noinspection PhpUnused */

namespace app\common\lib\wechat;

use app\common\lib\http\Response;
use app\common\lib\wechat\entity\request\CardTemplateInfo;

class ElectronicInvoiceClient extends BaseClient
{
    /**
     * @const SceneWithWechatPay 微信支付场景
     */
    public const SceneWithWechatPay = 'WITH_WECHATPAY'; // 微信支付场景
    /**
     * @const SceneWithoutWechatPay 非微信支付场景
     */
    public const SceneWithoutWechatPay = 'WITHOUT_WECHATPAY';

    // 来源
    public const SourceWEB = 'WEB'; // WEB
    public const  SourceMiniprogram = 'MINIPROGRAM'; // 小程序


    /**
     * 创建电子发票卡券模板
     *
     * @param CardTemplateInfo $cardTemplateInformation 【卡券模板信息】 卡券模板信息
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/fapiao-card-template/create-fapiao-card-template.html
     * @return Response
     */
    public function CreateCardTemplate(CardTemplateInfo $cardTemplateInformation): Response
    {
        $response = $this->httpClient->setMethod('POST')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/card-template')
            ->setBody(json_encode([
                'card_appid' => $this->appid,
                'card_template_information' => $cardTemplateInformation->toArray()
            ]))
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }


    /**
     * 查询商户配置的开发选项
     *
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/fapiao-merchant/query-development-config.html
     * @return Response
     */
    public function getDevelopmentConfig(): Response
    {
        $response = $this->httpClient->setMethod('GET')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/merchant/development-config')
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    /**
     * 设置商户配置的开发选项
     *
     * @param string $callbackUrl 【商户回调地址】 商户/服务商需要设置回调地址，收取微信的授权通知、开票通知、插卡通知等相关通知
     * @param ?bool $showFapiaoCell [default=null] 【全部账单展示开发票入口开关】 指定全部账单是否展示开发票入口
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/fapiao-merchant/update-development-config.html
     * @return Response
     */
    public function setDevelopmentConfig(string $callbackUrl, ?bool $showFapiaoCell = null): Response
    {
        $body = [
            'callback_url' => $callbackUrl
        ];
        if (!is_null($showFapiaoCell)) {
            $body['show_fapiao_cell'] = $showFapiaoCell;
        }

        $response = $this->httpClient->setMethod('PATCH')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/merchant/development-config')
            ->setBody(json_encode($body))
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    /**
     * 获取抬头填写链接
     *
     * @param string $fapiaoApplyId 【发票申请单号】 发票申请单号，唯一标识一次开票行为。只能是字母、数字、中划线-、下划线_、竖线|、星号*这些英文半角字符，且该单号在每个商户下必须唯一。当用户完成抬头填写后，微信支付会以该单号回调商户，商户可通过【获取用户填写的抬头】接口查询用户填写的抬头信息
     * @param string $openid 用户OpenID】 需要填写发票抬头的用户在商户AppID下的OpenID
     * @param int $totalAmount 【总金额】 展示在抬头填写页面的开票金额，对该发票申请单开票时所有发票的总价税合计之和不能超过此金额，单位：分
     * @param string $source 【开票来源】 开票来源 WEB:微信H5 MINIPROGRAM:微信小程序
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/user-title/acquire-fapiao-title-url.html
     * @return Response
     */
    public function getTitleUrl(string $fapiaoApplyId, string $openid, int $totalAmount, string $source): Response
    {
        $response = $this->httpClient->setMethod('GET')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/user-title/title-url')
            ->setQuery([
                'fapiao_apply_id' => $fapiaoApplyId,
                'appid' => $this->appid,
                'openid' => $openid,
                'total_amount' => $totalAmount,
                'source' => $source
            ])
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    /**
     * 获取用户填写的抬头
     *
     * @param string $fapiaoApplyId 【发票申请单号】
     * @param string $scene 开票场景
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/user-title/get-user-title.html
     * @return Response
     */
    public function getUserTitle(string $fapiaoApplyId, string $scene): Response
    {
        $response = $this->httpClient->setMethod('GET')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/user-title')
            ->setQuery([
                'fapiao_apply_id' => $fapiaoApplyId,
                'scene' => $scene
            ])
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    /**
     * 上传电子发票文件
     *
     * @param string $filePath 【电子发票文件路径】电子发票文件的本地路径
     * @param string $fileType 【文件类型】发票文件的类型，目前只支持 PDF
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/fapiao-card/upload-fapiao-file.html
     * @return Response
     */
    public function uploadFapiaoFile(string $filePath, string $fileType = 'PDF'): Response
    {
        // 检查文件是否存在
        if (!file_exists($filePath)) {
            throw new \InvalidArgumentException('电子发票文件不存在: ' . $filePath);
        }

        // 读取文件内容
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            throw new \RuntimeException('无法读取电子发票文件: ' . $filePath);
        }

        // 计算文件摘要（SM3算法）
        $digest = $this->calculateSM3Digest($fileContent);

        // 构建元信息
        $meta = [
            'file_type' => $fileType,
            'digest_alogrithm' => 'SM3',
            'digest' => $digest
        ];

        // 构建multipart表单数据
        $boundary = '----WebKitFormBoundary' . uniqid();
        $body = $this->buildMultipartBody($fileContent, $meta, $boundary);

        $response = $this->httpClient->setMethod('POST')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/fapiao-applications/upload-fapiao-file')
            ->setBody($body)
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-Type' => 'multipart/form-data; boundary=' . $boundary,
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                )
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }

    /**
     * 将电子发票插入微信用户卡包
     *
     * @param string $fapiaoApplyId 【发票申请单号】发票申请单号，唯一标识一次开票行为
     * @param string $scene 【插卡场景】插卡场景，WITH_WECHATPAY 或 WITHOUT_WECHATPAY
     * @param array $buyerInformation 【购买方信息】购买方信息，即发票抬头
     * @param array $fapiaoCardInformation 【电子发票卡券信息列表】电子发票卡券信息列表，最多五条
     * @document https://pay.weixin.qq.com/docs/merchant/apis/fapiao/fapiao-card/insert-cards.html
     * @return Response
     */
    public function insertFapiaoCards(string $fapiaoApplyId, string $scene, array $buyerInformation, array $fapiaoCardInformation): Response
    {
        // 加密敏感字段
        $encryptedBuyerInfo = $this->encryptSensitiveFields($buyerInformation);

        $body = [
            'scene' => $scene,
            'buyer_information' => $encryptedBuyerInfo,
            'fapiao_card_information' => $fapiaoCardInformation
        ];

        $response = $this->httpClient->setMethod('POST')->setProtocol('https')
            ->setDomain('api.mch.weixin.qq.com')
            ->setUri('v3/new-tax-control-fapiao/fapiao-applications/' . $fapiaoApplyId . '/insert-cards')
            ->setBody(json_encode($body))
            ->setHeader([
                'User-Agent' => env('UA'),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => $this->sign->generateSign(
                    $this->httpClient->getMethod(),
                    $this->httpClient->getUrl(),
                    $this->httpClient->getBody()
                ),
                'Wechatpay-Serial' => config('wechat.weixin_pingtai_sn')
            ])->sendRequest();

        $this->httpClient->reset();

        return $response;
    }